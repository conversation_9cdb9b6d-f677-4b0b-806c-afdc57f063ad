using System;
using System.Collections.Generic;
using HeroYulgang.Helpers;

namespace RxjhServer.NpcManager
{
    /// <summary>
    /// Test class to verify the improved NPC behavior logic
    /// </summary>
    public static class NPCBehaviorTest
    {
        /// <summary>
        /// Test the new smart positioning system
        /// </summary>
        public static void TestSmartPositioning()
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "=== Testing NPC Smart Positioning System ===");
                
                // Create a mock player
                var mockPlayer = CreateMockPlayer(100, 100, 1001);
                
                // Create multiple NPCs around the player
                var npcs = new List<NpcClass>();
                for (int i = 0; i < 5; i++)
                {
                    var npc = CreateMockNPC(i + 1000, 90 + i * 5, 90 + i * 5, 1001);
                    npcs.Add(npc);
                }
                
                LogHelper.WriteLine(LogLevel.Info, $"Created {npcs.Count} NPCs around player at ({mockPlayer.PosX}, {mockPlayer.PosY})");
                
                // Test chase behavior for each NPC
                foreach (var npc in npcs)
                {
                    LogHelper.WriteLine(LogLevel.Info, $"Testing NPC {npc.NPC_SessionID} chase behavior...");
                    
                    // Simulate chase behavior
                    var behavior = NPCBehaviorManager.DetermineBehavior(npc, mockPlayer);
                    LogHelper.WriteLine(LogLevel.Info, $"NPC {npc.NPC_SessionID} behavior: {behavior}");
                    
                    if (behavior == NPCBehaviorType.Chase || behavior == NPCBehaviorType.DirectAttack)
                    {
                        LogHelper.WriteLine(LogLevel.Info, $"NPC {npc.NPC_SessionID} will chase/attack player - positioning system will prevent clustering");
                    }
                }
                
                LogHelper.WriteLine(LogLevel.Info, "=== Smart Positioning Test Completed ===");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in TestSmartPositioning: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Test the improved idle behavior
        /// </summary>
        public static void TestImprovedIdleBehavior()
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "=== Testing Improved Idle Behavior ===");
                
                // Create an NPC far from spawn
                var npc = CreateMockNPC(2000, 200, 200, 1001);
                npc.Rxjh_cs_X = 100; // Spawn point
                npc.Rxjh_cs_Y = 100;
                
                LogHelper.WriteLine(LogLevel.Info, $"NPC {npc.NPC_SessionID} at ({npc.Rxjh_X}, {npc.Rxjh_Y}), spawn at ({npc.Rxjh_cs_X}, {npc.Rxjh_cs_Y})");
                
                var distanceFromSpawn = NPCBehaviorManager.GetDistanceFromSpawn(npc);
                LogHelper.WriteLine(LogLevel.Info, $"Distance from spawn: {distanceFromSpawn}");
                
                if (distanceFromSpawn > 100)
                {
                    LogHelper.WriteLine(LogLevel.Info, "NPC is far from spawn - idle behavior will bias movement towards spawn");
                }
                else
                {
                    LogHelper.WriteLine(LogLevel.Info, "NPC is near spawn - idle behavior will use natural random movement");
                }
                
                LogHelper.WriteLine(LogLevel.Info, "=== Improved Idle Behavior Test Completed ===");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in TestImprovedIdleBehavior: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Test attack range positioning
        /// </summary>
        public static void TestAttackRangePositioning()
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "=== Testing Attack Range Positioning ===");
                
                var mockPlayer = CreateMockPlayer(100, 100, 1001);
                var npc = CreateMockNPC(3000, 150, 150, 1001);
                
                var distance = NPCBehaviorManager.CalculateDistance(npc.Rxjh_X, npc.Rxjh_Y, mockPlayer.PosX, mockPlayer.PosY);
                LogHelper.WriteLine(LogLevel.Info, $"Initial distance between NPC and player: {distance}");
                LogHelper.WriteLine(LogLevel.Info, $"Attack range: {NPCBehaviorManager.ATTACK_RANGE}");
                
                if (distance > NPCBehaviorManager.ATTACK_RANGE)
                {
                    LogHelper.WriteLine(LogLevel.Info, "NPC will move to optimal attack position (not player's exact location)");
                    LogHelper.WriteLine(LogLevel.Info, "This prevents NPCs from clustering on top of the player");
                }
                else
                {
                    LogHelper.WriteLine(LogLevel.Info, "NPC is already in attack range - will attack without moving");
                }
                
                LogHelper.WriteLine(LogLevel.Info, "=== Attack Range Positioning Test Completed ===");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in TestAttackRangePositioning: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Run all tests
        /// </summary>
        public static void RunAllTests()
        {
            LogHelper.WriteLine(LogLevel.Info, "Starting NPC Behavior Tests...");
            
            TestSmartPositioning();
            TestImprovedIdleBehavior();
            TestAttackRangePositioning();
            
            LogHelper.WriteLine(LogLevel.Info, "All NPC Behavior Tests Completed!");
        }
        
        #region Mock Objects for Testing
        
        private static Players CreateMockPlayer(float x, float y, int mapId)
        {
            // This is a simplified mock - in real testing you'd need to properly initialize a Players object
            // For now, we'll assume the Players class has a constructor or can be initialized
            var player = new Players();
            // Note: You may need to adjust these property assignments based on the actual Players class structure
            // player.PosX = x;
            // player.PosY = y;
            // player.MapID = mapId;
            return player;
        }
        
        private static NpcClass CreateMockNPC(int sessionId, float x, float y, int mapId)
        {
            var npc = new NpcClass();
            npc.NPC_SessionID = sessionId;
            npc.Rxjh_X = x;
            npc.Rxjh_Y = y;
            npc.Rxjh_Map = mapId;
            npc.FLD_AT = 100; // Set attack power so NPC is considered active
            npc.FLD_AUTO = 1; // Make it aggressive
            npc.NPCDeath = false;
            return npc;
        }
        
        #endregion
    }
}
